'use client';
import { useCreateContract } from '@/apis/contracts/contracts.api';
import { IContract } from '@/apis/contracts/contracts.type';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormCreateAsic from '../../_components/Form/FormCreateAsic';
import { showToastSuccess } from '@/utils/toast-message';

const CreateAsic = () => {
    const router = useRouter();
    const { mutate: createContract } = useCreateContract({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới hợp đồng thương mại thành công',
                message:
                    'Thông tin hợp đồng thương mại đã được tạo mới thành công trong hệ thống.',
            });

            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        createContract(payload as IContract);
    };
    const handleClose = () => {
        router.back();
    };

    return <FormCreateAsic onSubmit={handleSubmit} onClose={handleClose} />;
};

export default CreateAsic;
